track_high_thresh:
  type: uniform
  default: 0.6  # from the default parameters
  range: [0.3, 0.7]

track_low_thresh:
  type: uniform
  default: 0.1  # from the default parameters
  range: [0.1, 0.3]

new_track_thresh:
  type: uniform
  default: 0.7  # from the default parameters
  range: [0.1, 0.8]

track_buffer:
  type: randint
  default: 30  # from the default parameters
  range: [20, 81]

match_thresh:
  type: uniform
  default: 0.8  # from the default parameters
  range: [0.1, 0.9]

proximity_thresh:
  type: uniform
  default: 0.5  # from the default parameters
  range: [0.25, 0.75]

appearance_thresh:
  type: uniform
  default: 0.25  # from the default parameters
  range: [0.1, 0.8]

cmc_method:
  type: choice
  default: ecc  # from the default parameters
  options: [sof, ecc]