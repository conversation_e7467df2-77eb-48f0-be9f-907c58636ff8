site_name: ""
theme:
  name: material
  logo: logo/logo.png
  features:
    - navigation.sections
    - navigation.expand

markdown_extensions:
  - mkdocs-click
  - md_in_html
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences

plugins:
  - search
  - include-markdown
  - mkdocstrings:
      handlers:
        python:
          options:
            selection:
              members: "__all__"
              filters:
                - "!^update$"

nav:
  - Home: index.md
  - Quickstart: quickstart.md
  - CLI:
      - Track:        modes/track.md
      - Generate:     modes/generate.md
      - Evaluate:     modes/eval.md
      - Tune:         modes/tune.md
  - Trackers:
      - ByteTrack:    trackers/bytetrack.md
      - BotSort:      trackers/botsort.md
      - StrongSort:   trackers/strongsort.md
      - OcSort:       trackers/ocsort.md
      - DeepOcSort:   trackers/deepocsort.md
      # - HybridSort:   api/trackers/hybridsort.md
      - BoostTrack:   trackers/boosttrack.md
