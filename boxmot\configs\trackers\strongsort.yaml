min_conf:
  type: uniform
  default: 0.6  # from the default parameters
  range: [0.2, 0.8]

ema_alpha:
  type: uniform
  default: 0.9  # from the default parameters
  range: [0.7, 0.95]

max_cos_dist:
  type: uniform
  default: 0.4  # from the default parameters
  range: [0.1, 0.4]

max_iou_dist:
  type: uniform
  default: 0.7  # from the default parameters
  range: [0.5, 0.95]

max_age:
  type: randint
  default: 30  # from the default parameters
  range: [10, 151]  # upper bound exclusive

n_init:
  type: randint
  default: 3  # from the default parameters
  range: [1, 4]  # upper bound exclusive

mc_lambda:
  type: uniform
  default: 0.98  # from the default parameters
  range: [0.90, 0.999]

nn_budget:
  type: choice
  default: 100  # from the default parameters
  options: [100]
