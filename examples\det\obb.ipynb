{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\r"]}], "source": ["import cv2\n", "import numpy as np\n", "\n", "from boxmot import OcSort\n", "\n", "# Initialize the tracker\n", "tracker = OcSort(\n", "    asso_func=\"centroid\",\n", "    min_hits=10,\n", "    asso_threshold=0.98,\n", "    det_thresh = 0.7,\n", "    max_age=20,\n", "    use_byte=True,\n", "    Q_xy_scaling = 0.01,\n", "    Q_s_scaling = 0.0001,\n", ")\n", "\n", "def get_parabolic_array(i):\n", "    \"\"\"\n", "    Generates coordinates where x increases linearly,\n", "    y follows a parabolic curve (y = base + coeff * i^2),\n", "    and the angle matches the tangent of the curve.\n", "    \"\"\"\n", "    # -- FIRST ROW --\n", "    # Define x1, y1\n", "    x1 = 144 + i\n", "    y1 = 212 + 0.01 * (i ** 2)\n", "    \n", "    # Slope = 2 * coeff * i => for 0.01 * i^2, slope = 2*0.01*i = 0.02*i\n", "    slope1 = 0.02 * i\n", "    # Angle in radians\n", "    angle1 = np.arctan(slope1)\n", "    \n", "    # -- SECOND ROW --\n", "    # Define x2, y2 with a different coefficient\n", "    x2 = 425 + i\n", "    y2 = 281 + 0.02 * (i ** 2)\n", "    \n", "    # Slope for 0.02 * i^2 is 2*0.02*i = 0.04*i\n", "    slope2 = 0.04 * i\n", "    # Angle in radians\n", "    angle2 = np.arctan(slope2)\n", "    \n", "    # Build the array\n", "    det = np.array([\n", "        [x1, y1, 45, 30, angle1, 0.82,  0],   # row 1\n", "        [x2, y2, 45, 30, angle2, 0.72, 65]    # row 2\n", "    ])\n", "    \n", "    return det\n", "\n", "\n", "for i in range(0, 100):\n", "\n", "    #frame = cv2.imread(str(img_dir / (file.stem + '.png')))\n", "    frame = np.zeros((1080,1080,3))\n", "    \n", "    det = get_parabolic_array(i)\n", "\n", "    # Update the tracker\n", "    res = tracker.update(det, frame)  # --> M X (x, y, x, y, id, conf, cls, ind)\n", "    \n", "    # Plot tracking results on the image\n", "    tracker.plot_results(frame, show_trajectories=True, fontscale=2, thickness=4)\n", "\n", "    # Display the frame\n", "    cv2.imshow('BoXMOT', frame)\n", "\n", "    print(len(tracker.active_tracks),end='\\r')\n", "\n", "    key = cv2.wait<PERSON><PERSON>(1) & 0xFF\n", "    if key == ord('q') or key ==27:\n", "        break\n", "\n", "cv2.destroyAllWindows()"]}], "metadata": {"kernelspec": {"display_name": "boxmot-YDNZdsaB-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}