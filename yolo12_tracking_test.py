from pathlib import Path

import cv2
import torch
import numpy as np
from boxmot import BotSort
from ultralytics import YOLO

model = YOLO("yolo12x.pt")
source = "D:/Media/Highway/bili/bilibili_BV19x4y1n7ws_480x480.mp4"

color = (0, 0, 255)  # BGR
thickness = 2
fontscale = 0.5

device = "cuda:0" # cuda:0 , cpu

# device = torch.device('cuda')

# Initialize tracker
tracker = BotSort(reid_weights=Path('osnet_x0_25_msmt17.pt'), device=device, half=False)

# Video capture setup
vid = cv2.VideoCapture(source)

while True:
    ret, frame = vid.read()
    if not ret:
        break

    # Perform detection
    detections = model.predict(frame)
    if (len(detections) < 1):
        continue

    print(detections[0].boxes)

    ts = tracker.update(np.array(detections[0].boxes.data.tolist()), frame)




    # Plot tracking results on the image
    # tracker.plot_results(frame, show_trajectories=True)

    cv2.imshow('BoXMOT + YOLO12', frame)

    # Press 'q' to exit
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

# Release resources
vid.release()
cv2.destroyAllWindows()

# results = model.track(source=source, save=True, show=True)
# for r in results:
#     print(r.boxes.id)  # print tracking IDs

