name: Bug
# title: " "
description: Report a BoxMOT bug
labels: [bug]
body:

  - type: checkboxes
    attributes:
      label: Search before asking
      description: >
        Please search the [issues](https://github.com/mikel-brostrom/boxmot/issues) and [discussions](https://github.com/mikel-brostrom/boxmot/discussions) to see if a similar question already exists.
      options:
        - label: >
            I have searched the BoxMOT [issues](https://github.com/mikel-brostrom/boxmot/issues) and [discussions](https://github.com/mikel-brostrom/boxmot/discussions) and found no similar questions.
          required: true

  - type: textarea
    attributes:
      label: Bug
      description: Provide console output with error messages and/or screenshots of the bug.
      placeholder: |
        💡 ProTip! Include as much information as possible (screenshots, logs, tracebacks etc.) to receive the most helpful response.
    validations:
      required: true

  - type: textarea
    attributes:
      label: Environment
      description: Please specify the environment information you used to produce the bug.
      placeholder: |
        - boxmot v12.0.0
        - Python 3.11.5
        - torch-2.6.0MPS

    validations:
      required: true

  - type: textarea
    attributes:
      label: Minimal Reproducible Example
      description: >
        When asking a question, people will be better able to provide help if you provide code that they can easily understand and use to [**reproduce**](https://stackoverflow.com/help/minimal-reproducible-example) the problem.
      placeholder: |
        ```
        # Code to reproduce your issue here
        ```
    validations:
      required: true
