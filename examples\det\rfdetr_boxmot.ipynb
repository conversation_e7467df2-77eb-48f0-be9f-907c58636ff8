{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: rfdetr in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (1.0.8)\n", "Requirement already satisfied: cython in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (3.0.12)\n", "Requirement already satisfied: pycocotools in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (2.0.8)\n", "Requirement already satisfied: torch>=1.13.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (2.2.2)\n", "Requirement already satisfied: torchvision>=0.14.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (0.17.2)\n", "Requirement already satisfied: fairscale in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (0.4.13)\n", "Requirement already satisfied: scipy in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (1.13.1)\n", "Requirement already satisfied: timm in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (1.0.15)\n", "Requirement already satisfied: tqdm in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (4.67.1)\n", "Requirement already satisfied: numpy in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (1.26.4)\n", "Requirement already satisfied: accelerate in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (1.5.2)\n", "Requirement already satisfied: transformers in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (4.50.2)\n", "Requirement already satisfied: peft in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (0.15.1)\n", "Requirement already satisfied: ninja in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (1.11.1.4)\n", "Requirement already satisfied: einops in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (0.8.1)\n", "Requirement already satisfied: wandb in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (0.19.8)\n", "Requirement already satisfied: pandas in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (2.2.3)\n", "Requirement already satisfied: pylabel in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (0.1.55)\n", "Requirement already satisfied: onnx in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (1.16.1)\n", "Requirement already satisfied: onnxsim in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (0.4.36)\n", "Requirement already satisfied: onnx_graphsurgeon in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (0.5.7)\n", "Requirement already satisfied: polygraphy in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (0.49.20)\n", "Requirement already satisfied: open_clip_torch in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (2.31.0)\n", "Requirement already satisfied: rf100vl in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (1.0.0)\n", "Requirement already satisfied: pydantic in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (2.11.0)\n", "Requirement already satisfied: supervision in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rfdetr) (0.25.1)\n", "Requirement already satisfied: filelock in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from torch>=1.13.0->rfdetr) (3.18.0)\n", "Requirement already satisfied: typing-extensions>=4.8.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from torch>=1.13.0->rfdetr) (4.12.2)\n", "Requirement already satisfied: sympy in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from torch>=1.13.0->rfdetr) (1.13.3)\n", "Requirement already satisfied: networkx in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from torch>=1.13.0->rfdetr) (3.1)\n", "Requirement already satisfied: jinja2 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from torch>=1.13.0->rfdetr) (3.1.6)\n", "Requirement already satisfied: fsspec in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from torch>=1.13.0->rfdetr) (2025.3.0)\n", "Requirement already satisfied: pillow!=8.3.*,>=5.3.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from torchvision>=0.14.0->rfdetr) (11.1.0)\n", "Requirement already satisfied: packaging>=20.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from accelerate->rfdetr) (24.2)\n", "Requirement already satisfied: psutil in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from accelerate->rfdetr) (7.0.0)\n", "Requirement already satisfied: pyyaml in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from accelerate->rfdetr) (6.0.2)\n", "Requirement already satisfied: huggingface-hub>=0.21.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from accelerate->rfdetr) (0.29.3)\n", "Requirement already satisfied: safetensors>=0.4.3 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from accelerate->rfdetr) (0.5.3)\n", "Requirement already satisfied: protobuf>=3.20.2 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from onnx->rfdetr) (5.29.4)\n", "Requirement already satisfied: rich in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from onnxsim->rfdetr) (13.9.4)\n", "Requirement already satisfied: regex in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from open_clip_torch->rfdetr) (2024.11.6)\n", "Requirement already satisfied: ftfy in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from open_clip_torch->rfdetr) (6.3.1)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from pandas->rfdetr) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from pandas->rfdetr) (2025.1)\n", "Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from pandas->rfdetr) (2025.1)\n", "Requirement already satisfied: matplotlib>=2.1.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from pycocotools->rfdetr) (3.9.4)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from pydantic->rfdetr) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from pydantic->rfdetr) (2.33.0)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from pydantic->rfdetr) (0.4.0)\n", "Requirement already satisfied: bbox-visualizer in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from pylabel->rfdetr) (0.2.0)\n", "Requirement already satisfied: opencv-python in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from pylabel->rfdetr) (4.11.0.86)\n", "Requirement already satisfied: scikit-learn in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from pylabel->rfdetr) (1.6.1)\n", "Requirement already satisfied: jupyter-bbox-widget in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from pylabel->rfdetr) (0.6.0)\n", "Requirement already satisfied: roboflow in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rf100vl->rfdetr) (1.1.58)\n", "Requirement already satisfied: contourpy>=1.0.7 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from supervision->rfdetr) (1.3.0)\n", "Requirement already satisfied: defusedxml<0.8.0,>=0.7.1 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from supervision->rfdetr) (0.7.1)\n", "Requirement already satisfied: requests>=2.26.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from supervision->rfdetr) (2.32.3)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from transformers->rfdetr) (0.21.1)\n", "Requirement already satisfied: click!=8.0.0,>=7.1 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from wandb->rfdetr) (8.1.8)\n", "Requirement already satisfied: docker-pycreds>=0.4.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from wandb->rfdetr) (0.4.0)\n", "Requirement already satisfied: gitpython!=3.1.29,>=1.0.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from wandb->rfdetr) (3.1.44)\n", "Requirement already satisfied: platformdirs in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from wandb->rfdetr) (4.3.7)\n", "Requirement already satisfied: sentry-sdk>=2.0.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from wandb->rfdetr) (2.24.1)\n", "Requirement already satisfied: setproctitle in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from wandb->rfdetr) (1.3.5)\n", "Requirement already satisfied: setuptools in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from wandb->rfdetr) (69.1.0)\n", "Requirement already satisfied: six>=1.4.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from docker-pycreds>=0.4.0->wandb->rfdetr) (1.17.0)\n", "Requirement already satisfied: gitdb<5,>=4.0.1 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from gitpython!=3.1.29,>=1.0.0->wandb->rfdetr) (4.0.12)\n", "Requirement already satisfied: cycler>=0.10 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from matplotlib>=2.1.0->pycocotools->rfdetr) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from matplotlib>=2.1.0->pycocotools->rfdetr) (4.56.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from matplotlib>=2.1.0->pycocotools->rfdetr) (1.4.7)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from matplotlib>=2.1.0->pycocotools->rfdetr) (3.2.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from requests>=2.26.0->supervision->rfdetr) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from requests>=2.26.0->supervision->rfdetr) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from requests>=2.26.0->supervision->rfdetr) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from requests>=2.26.0->supervision->rfdetr) (2025.1.31)\n", "Requirement already satisfied: wcwidth in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from ftfy->open_clip_torch->rfdetr) (0.2.13)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from jinja2->torch>=1.13.0->rfdetr) (3.0.2)\n", "Requirement already satisfied: anywidget>=0.9.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from jupyter-bbox-widget->pylabel->rfdetr) (0.9.18)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rich->onnxsim->rfdetr) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from rich->onnxsim->rfdetr) (2.19.1)\n", "Requirement already satisfied: opencv-python-headless==********* in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from roboflow->rf100vl->rfdetr) (*********)\n", "Requirement already satisfied: pillow-heif>=0.18.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from roboflow->rf100vl->rfdetr) (0.22.0)\n", "Requirement already satisfied: python-dotenv in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from roboflow->rf100vl->rfdetr) (1.1.0)\n", "Requirement already satisfied: requests-toolbelt in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from roboflow->rf100vl->rfdetr) (1.0.0)\n", "Requirement already satisfied: filetype in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from roboflow->rf100vl->rfdetr) (1.2.0)\n", "Requirement already satisfied: joblib>=1.2.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from scikit-learn->pylabel->rfdetr) (1.4.2)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from scikit-learn->pylabel->rfdetr) (3.6.0)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from sympy->torch>=1.13.0->rfdetr) (1.3.0)\n", "Requirement already satisfied: ipywidgets>=7.6.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (8.1.5)\n", "Requirement already satisfied: psygnal>=0.8.1 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (0.12.0)\n", "Requirement already satisfied: smmap<6,>=3.0.1 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from gitdb<5,>=4.0.1->gitpython!=3.1.29,>=1.0.0->wandb->rfdetr) (5.0.2)\n", "Requirement already satisfied: mdurl~=0.1 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from markdown-it-py>=2.2.0->rich->onnxsim->rfdetr) (0.1.2)\n", "Requirement already satisfied: comm>=0.1.3 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from ipywidgets>=7.6.0->anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (0.2.2)\n", "Requirement already satisfied: ipython>=6.1.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from ipywidgets>=7.6.0->anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (8.18.1)\n", "Requirement already satisfied: traitlets>=4.3.1 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from ipywidgets>=7.6.0->anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (5.14.3)\n", "Requirement already satisfied: widgetsnbextension~=4.0.12 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from ipywidgets>=7.6.0->anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (4.0.13)\n", "Requirement already satisfied: jupyterlab-widgets~=3.0.12 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from ipywidgets>=7.6.0->anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (3.0.13)\n", "Requirement already satisfied: decorator in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from ipython>=6.1.0->ipywidgets>=7.6.0->anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (5.2.1)\n", "Requirement already satisfied: jedi>=0.16 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from ipython>=6.1.0->ipywidgets>=7.6.0->anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (0.19.2)\n", "Requirement already satisfied: matplotlib-inline in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from ipython>=6.1.0->ipywidgets>=7.6.0->anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (0.1.7)\n", "Requirement already satisfied: prompt-toolkit<3.1.0,>=3.0.41 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from ipython>=6.1.0->ipywidgets>=7.6.0->anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (3.0.50)\n", "Requirement already satisfied: stack-data in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from ipython>=6.1.0->ipywidgets>=7.6.0->anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (0.6.3)\n", "Requirement already satisfied: pexpect>4.3 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from ipython>=6.1.0->ipywidgets>=7.6.0->anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (4.9.0)\n", "Requirement already satisfied: parso<0.9.0,>=0.8.4 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from jedi>=0.16->ipython>=6.1.0->ipywidgets>=7.6.0->anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (0.8.4)\n", "Requirement already satisfied: ptyprocess>=0.5 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from pexpect>4.3->ipython>=6.1.0->ipywidgets>=7.6.0->anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (0.7.0)\n", "Requirement already satisfied: executing>=1.2.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from stack-data->ipython>=6.1.0->ipywidgets>=7.6.0->anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (2.2.0)\n", "Requirement already satisfied: asttokens>=2.1.0 in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from stack-data->ipython>=6.1.0->ipywidgets>=7.6.0->anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (3.0.0)\n", "Requirement already satisfied: pure-eval in /Users/<USER>/Library/Caches/pypoetry/virtualenvs/boxmot-YDNZdsaB-py3.11/lib/python3.11/site-packages (from stack-data->ipython>=6.1.0->ipywidgets>=7.6.0->anywidget>=0.9.0->jupyter-bbox-widget->pylabel->rfdetr) (0.2.3)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.0\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install rfdetr"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-03-28 12:02:18.458\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mboxmot.utils.torch_utils\u001b[0m:\u001b[36mselect_device\u001b[0m:\u001b[36m52\u001b[0m - \u001b[1mYolo Tracking v12.0.2 🚀 Python-3.11.5 torch-2.2.2CPU\u001b[0m\n", "\u001b[32m2025-03-28 12:02:18.479\u001b[0m | \u001b[32m\u001b[1mSUCCESS \u001b[0m | \u001b[36mboxmot.appearance.reid_model_factory\u001b[0m:\u001b[36mload_pretrained_weights\u001b[0m:\u001b[36m183\u001b[0m - \u001b[32m\u001b[1mLoaded pretrained weights from osnet_x0_25_msmt17.pt\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading pretrain weights\n"]}], "source": ["from pathlib import Path\n", "\n", "import cv2\n", "import numpy as np\n", "import torch\n", "from PIL import Image\n", "from rfdetr import RFDETRBase\n", "\n", "from boxmot import BotSort\n", "\n", "# Load RFDETR model\n", "model = RFDETRBase(device='cpu')\n", "\n", "# Initialize the tracker\n", "tracker = BotSort(\n", "    reid_weights=Path(\"osnet_x0_25_msmt17.pt\"),  # Path to ReID model\n", "    device=torch.device(\"cpu\"),  # Change to 'cuda' if using GPU\n", "    half=False\n", ")\n", "\n", "# Open the video file (0 for webcam or path to a video file)\n", "vid = cv2.VideoCapture(0)\n", "\n", "while True:\n", "    # Capture frame-by-frame\n", "    ret, frame = vid.read()\n", "    if not ret:\n", "        break\n", "\n", "    # Convert frame to PIL Image format for RFDETR\n", "    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)\n", "    image = Image.fromarray(frame_rgb)\n", "\n", "    # Perform detection\n", "    detections = model.predict(image, threshold=0.5)\n", "    \n", "    # Convert detections to numpy array in format: (x1, y1, x2, y2, conf, cls)\n", "    dets = np.column_stack((\n", "        detections.xyxy,  # Bounding boxes (x1, y1, x2, y2)\n", "        detections.confidence,  # Confidence scores\n", "        detections.class_id.astype(int)  # Class IDs\n", "    ))\n", "\n", "    # Update the tracker\n", "    res = tracker.update(dets, frame)  # M X (x1, y1, x2, y2, id, conf, cls, ind)\n", "\n", "    # Plot tracking results on the image\n", "    tracker.plot_results(frame, show_trajectories=True)\n", "\n", "    cv2.imshow('BoXMOT + Torchvision', frame)\n", "\n", "    # Press 'q' to exit\n", "    if cv2.wait<PERSON><PERSON>(1) & 0xFF == ord('q'):\n", "        break\n", "\n", "# Release resources\n", "vid.release()\n", "cv2.destroyAllWindows()"]}], "metadata": {"kernelspec": {"display_name": "boxmot-YDNZdsaB-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}