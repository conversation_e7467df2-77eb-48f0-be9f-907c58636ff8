name: Publish to Py<PERSON>

on:
  push:
    branches: [main]
  workflow_dispatch:
    inputs:
      pypi:
        description: 'Target repository'
        required: true
        type: choice
        default: 'pypi'
        options:
          - pypi
          - testpypi
      bump_type:
        description: 'Version bump type'
        required: true
        type: choice
        default: 'patch'
        options:
          - patch
          - minor
          - major

jobs:
  pypi-upload:
    name: Publish
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          # Personal Access Token (PAT) so that git push
          # is authenticated such that you and can bypass the protected ref
          token: ${{ secrets.RELEASE_PAT }}

      - name: Set up Python environment
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip setuptools wheel uv poetry
          uv sync

      - name: Increase uv path version and add
        id: get_version
        run: |
          source .venv/bin/activate
          git config --local user.email <EMAIL>
          git config --local user.name mikel-brostrom
          bump_type=${{ github.event.inputs.bump_type }}
          commit_message=$(poetry version $bump_type)
          new_version=$(echo $commit_message | grep -oE '[0-9]+\.[0-9]+\.[0-9]+$')
          git add pyproject.toml
          uv build --no-sources
          echo "commit_message=$commit_message" >> $GITHUB_OUTPUT
          echo "new_version=$new_version" >> $GITHUB_OUTPUT
          du -sh dist/*
        if: ${{ success() }}

      - name: Update __init__.py version and add
        run: |
          sed -i "s/__version__ = '.*'/__version__ = '${{ steps.get_version.outputs.new_version }}'/" boxmot/__init__.py
          git add boxmot/__init__.py
        if: ${{ success() }}

      - name: Update citation pkg version and add
        run: |
          sed -i "s/version: .*/version: ${{ steps.get_version.outputs.new_version }}/" CITATION.cff
          git add CITATION.cff
        if: ${{ success() }}

      - name: Commit and push updated version
        run: |
          if [ "${{ github.event.inputs.pypi }}" == "pypi" ]; then
            git commit -m "${{ steps.get_version.outputs.commit_message }}"
            git push
          fi
        if: ${{ success() }}

      - name: Publish to PyPI
        run: |
          if [ "${{ github.event.inputs.pypi }}" == "pypi" ]; then
            uv publish --token ${{ secrets.PYPI_TOKEN }}
          else
            uv publish --index testpypi --token ${{ secrets.TEST_PYPI_TOKEN }}
          fi
        if: ${{ success() }}

      - name: Create code release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ steps.get_version.outputs.new_version }}
          release_name: Release v${{ steps.get_version.outputs.new_version }}
          draft: false
          prerelease: false
        if: ${{ success() }}
