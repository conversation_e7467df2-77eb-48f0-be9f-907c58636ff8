from pathlib import Path

import cv2
import numpy as np
import torch
from PIL import Image
from rfdetr import RFDETRBase

from boxmot import BotSort

# Load RFDETR model
model = RFDETRBase(device='cpu')

# Initialize the tracker
tracker = BotSort(
    reid_weights=Path("osnet_x0_25_msmt17.pt"),  # Path to ReID model
    device=torch.device("cuda:0"),  # Change to 'cuda' if using GPU
    half=False
)

# Open the video file (0 for webcam or path to a video file)
source = "D:/Media/Highway/bili/bilibili_BV19x4y1n7ws_480x480.mp4"
vid = cv2.VideoCapture(source)

while True:
    # Capture frame-by-frame
    ret, frame = vid.read()
    if not ret:
        break

    # Convert frame to PIL Image format for RFDETR
    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    image = Image.fromarray(frame_rgb)

    # Perform detection
    detections = model.predict(image, threshold=0.5)

    # Convert detections to numpy array in format: (x1, y1, x2, y2, conf, cls)
    dets = np.column_stack((
        detections.xyxy,  # Bounding boxes (x1, y1, x2, y2)
        detections.confidence,  # Confidence scores
        detections.class_id.astype(int)  # Class IDs
    ))

    # Update the tracker
    res = tracker.update(dets, frame)  # M X (x1, y1, x2, y2, id, conf, cls, ind)

    # Plot tracking results on the image
    tracker.plot_results(frame, show_trajectories=True)

    cv2.imshow('BoXMOT + RTDETR', frame)

    # Press 'q' to exit
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

# Release resources
vid.release()
cv2.destroyAllWindows()
