<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2dc6ca6e-a820-40a7-996d-aeea54315351" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/yolo12_tracking.ipynb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pyproject.toml" beforeDir="false" afterPath="$PROJECT_DIR$/pyproject.toml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uv.lock" beforeDir="false" afterPath="$PROJECT_DIR$/uv.lock" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Jupyter Notebook" />
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;Eastnova&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/mikel-brostrom/boxmot.git&quot;,
    &quot;accountId&quot;: &quot;fcb99802-5cbf-4a8b-a087-b8e33e5ba74c&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="32OnJEoBJG6UmxDZBU245jCbhSG" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.rtdetr_tracking_test.executor": "Run",
    "Python.yolo12_tracking_test.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "D:/OpenSource/Tracking/boxmot",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\OpenSource\Tracking\boxmot" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-822845ee3bb5-JavaScript-PY-243.23654.177" />
        <option value="bundled-python-sdk-91d3a02ef49d-43b77aa2d136-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-243.23654.177" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2dc6ca6e-a820-40a7-996d-aeea54315351" name="Changes" comment="" />
      <created>1757300413254</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1757300413254</updated>
      <workItem from="1757300414354" duration="4300000" />
      <workItem from="1757322017026" duration="9953000" />
      <workItem from="1757384701814" duration="5525000" />
      <workItem from="1757398846724" duration="3533000" />
      <workItem from="1757489586055" duration="3777000" />
      <workItem from="1757559340434" duration="410000" />
      <workItem from="1757604824770" duration="1385000" />
      <workItem from="1757632088269" duration="3551000" />
      <workItem from="1757653572940" duration="4718000" />
      <workItem from="1757690762416" duration="2031000" />
      <workItem from="1757727975036" duration="4838000" />
      <workItem from="1757774282213" duration="5169000" />
      <workItem from="1757803663258" duration="6236000" />
      <workItem from="1757810751695" duration="7453000" />
      <workItem from="1757837325266" duration="182000" />
      <workItem from="1757839426307" duration="968000" />
      <workItem from="1757861292663" duration="5524000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/boxmot$rtdetr_tracking_test.coverage" NAME="rtdetr_tracking_test Coverage Results" MODIFIED="1757867999452" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/boxmot$yolo12_tracking_test.coverage" NAME="yolo12_tracking_test Coverage Results" MODIFIED="1757864856274" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>