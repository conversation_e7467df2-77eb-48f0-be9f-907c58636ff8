{"cells": [{"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-09-13T03:00:45.593584Z", "start_time": "2025-09-13T03:00:39.470252Z"}}, "source": ["from pathlib import Path\n", "\n", "import cv2\n", "import torch\n", "\n", "from boxmot import BotSort\n", "from ultralytics import YOLO\n", "\n", "model = YOLO(\"yolo12x.pt\")\n", "source = \"D:/Media/Highway/bili/bilibili_BV19x4y1n7ws_480x480.mp4\"\n", "\n", "device = torch.device('cuda')\n", "\n", "# Initialize tracker\n", "tracker = BotSort(reid_weights=Path('osnet_x0_25_msmt17.pt'), device=0, half=False)"], "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-09-13 11:00:39.601\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mboxmot.utils.torch_utils\u001b[0m:\u001b[36mselect_device\u001b[0m:\u001b[36m78\u001b[0m - \u001b[1mYolo Tracking v15.0.2 🚀 Python-3.11.10 torch-2.7.1+cu118\n", "CUDA:0 (NVIDIA GeForce RTX 3080 Ti Laptop GPU, 16384MiB)\u001b[0m\n", "\u001b[32m2025-09-13 11:00:39.601\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mboxmot.appearance.backends.base_backend\u001b[0m:\u001b[36mdownload_model\u001b[0m:\u001b[36m149\u001b[0m - \u001b[1m[PID 21180] Downloading ReID weights from https://drive.google.com/uc?id=1sSwXSUlj4_tHZequ_iZ8w_Jh0VaRQMqF → osnet_x0_25_msmt17.pt\u001b[0m\n", "Downloading...\n", "From: https://drive.google.com/uc?id=1sSwXSUlj4_tHZequ_iZ8w_Jh0VaRQMqF\n", "To: D:\\OpenSource\\Tracking\\boxmot\\osnet_x0_25_msmt17.pt\n", "100%|██████████| 3.06M/3.06M [00:01<00:00, 1.96MB/s]\n", "\u001b[32m2025-09-13 11:00:45.476\u001b[0m | \u001b[32m\u001b[1mSUCCESS \u001b[0m | \u001b[36mboxmot.appearance.reid.registry\u001b[0m:\u001b[36mload_pretrained_weights\u001b[0m:\u001b[36m64\u001b[0m - \u001b[32m\u001b[1mLoaded pretrained weights from osnet_x0_25_msmt17.pt\u001b[0m\n"]}], "execution_count": 2}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-09-13T03:13:58.492923Z", "start_time": "2025-09-13T03:13:52.484937Z"}}, "source": ["# # Video capture setup\n", "# vid = cv2.VideoCapture(source)\n", "#\n", "# while True:\n", "#     ret, frame = vid.read()\n", "#     if not ret:\n", "#         break\n", "#\n", "#     results = model.track(source=source, persist=True, save=True, stream=True)\n", "#     # detections = model.predict(frame)[0]\n", "#\n", "#     # Plot results and display\n", "#     tracker.plot_results(frame, show_trajectories=True)\n", "#     cv2.imshow('BoXMOT + YOLO12', frame)\n", "#\n", "#     if cv2.wait<PERSON><PERSON>(1) & 0xFF == ord('q'):\n", "#         break\n", "#\n", "# # Release resources\n", "# vid.release()\n", "# cv2.destroyAllWindows()\n", "\n", "model.track(source=source, persist=True, save=True, stream=True)"], "outputs": [], "execution_count": 4}], "metadata": {"kernelspec": {"display_name": "boxmot-YDNZdsaB-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}