name: Docker Image CI

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Extract version from pyproject.toml
        id: extract_version
        run: |
          version=$(python3 -c 'import tomllib, sys; data = tomllib.loads(open("pyproject.toml","r", encoding="utf-8").read()); sys.stdout.write(data["project"]["version"])')
          echo "VERSION=$version" >> "$GITHUB_ENV"

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          push: true
          tags: |
            boxmot/boxmot:${{ env.VERSION }}
            boxmot/boxmot:latest
