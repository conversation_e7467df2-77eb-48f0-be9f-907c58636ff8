"""
YOLO12 + BotSort Object Detection and Tracking Script

This script performs object detection and tracking on a video file using:
- YOLO12 for object detection
- BotSort algorithm for tracking detected objects
- OpenCV for video processing and display

Requirements:
- ultralytics (for YOLO12)
- boxmot (for BotSort tracker)
- opencv-python
- torch
- numpy

Usage:
    python yolo12_botsort_test.py

Make sure you have the following files in your working directory:
- yolo12x.pt (YOLO12 model weights)
- osnet_x0_25_msmt17.pt (ReID model weights for tracking)
- test.mp4 (input video file)
"""

from pathlib import Path
import cv2
import torch
import numpy as np
from boxmot import BotSort
from ultralytics import YOLO


def main():
    # Configuration
    yolo_model_path = "yolo12x.pt"
    reid_model_path = "osnet_x0_25_msmt17.pt"
    video_source = "D:/Media/Highway/bili/bilibili_BV19x4y1n7ws_480x480.mp4"
    device = "cuda:0" if torch.cuda.is_available() else "cpu"
    
    print(f"Using device: {device}")
    
    # Initialize YOLO12 model for object detection
    print(f"Loading YOLO12 model: {yolo_model_path}")
    try:
        model = YOLO(yolo_model_path)
        print("✓ YOLO12 model loaded successfully")
    except Exception as e:
        print(f"✗ Error loading YOLO12 model: {e}")
        return
    
    # Initialize BotSort tracker
    print(f"Loading BotSort tracker with ReID model: {reid_model_path}")
    try:
        tracker = BotSort(
            reid_weights=Path(reid_model_path),
            device=device,
            half=False
        )
        print("✓ BotSort tracker initialized successfully")
    except Exception as e:
        print(f"✗ Error initializing BotSort tracker: {e}")
        return
    
    # Open video file
    print(f"Opening video source: {video_source}")
    vid = cv2.VideoCapture(video_source)
    
    if not vid.isOpened():
        print(f"✗ Error: Could not open video source: {video_source}")
        return
    
    # Get video properties
    fps = int(vid.get(cv2.CAP_PROP_FPS))
    width = int(vid.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(vid.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(vid.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"✓ Video opened successfully")
    print(f"  Resolution: {width}x{height}")
    print(f"  FPS: {fps}")
    print(f"  Total frames: {total_frames}")
    print("\nStarting detection and tracking...")
    print("Press 'q' to quit, 'p' to pause/resume")
    
    frame_count = 0
    paused = False
    
    try:
        while True:
            if not paused:
                ret, frame = vid.read()
                if not ret:
                    print("End of video reached")
                    break
                
                frame_count += 1
                
                # Perform object detection with YOLO12
                results = model.predict(frame, verbose=False)
                
                # Check if any detections were found
                if len(results) > 0 and results[0].boxes is not None and len(results[0].boxes) > 0:
                    # Extract detection data: (x1, y1, x2, y2, conf, cls)
                    detections = results[0].boxes.data.cpu().numpy()
                    
                    # Update tracker with detections
                    # Input format: (x1, y1, x2, y2, conf, cls)
                    # Output format: (x1, y1, x2, y2, id, conf, cls, ind)
                    tracks = tracker.update(detections, frame)
                    
                    # Display detection and tracking information
                    if len(tracks) > 0:
                        print(f"Frame {frame_count}: {len(detections)} detections, {len(tracks)} tracks")
                else:
                    # No detections found, update tracker with empty array
                    tracks = tracker.update(np.empty((0, 6)), frame)
            
            # Plot tracking results on the frame
            tracker.plot_results(frame, show_trajectories=True)
            
            # Add frame information overlay
            cv2.putText(frame, f"Frame: {frame_count}/{total_frames}", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.putText(frame, f"Device: {device}", 
                       (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.putText(frame, "Press 'q' to quit, 'p' to pause", 
                       (10, height - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # Display the frame
            cv2.imshow('YOLO12 + BotSort Tracking', frame)
            
            # Handle keyboard input
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("Quit requested by user")
                break
            elif key == ord('p'):
                paused = not paused
                print(f"{'Paused' if paused else 'Resumed'}")
    
    except KeyboardInterrupt:
        print("\nInterrupted by user")
    except Exception as e:
        print(f"Error during processing: {e}")
    finally:
        # Clean up resources
        print("Cleaning up...")
        vid.release()
        cv2.destroyAllWindows()
        print("✓ Resources released successfully")


if __name__ == "__main__":
    main()
