# <PERSON><PERSON> 🔥 Yolo Tracking 🧾 AGPL-3.0 license

repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: check-added-large-files # prevents giant files from being committed.
        exclude: '.*vocab*.'
      - id: check-case-conflict # checks for files that would conflict in case-insensitive filesystems.
      - id: check-merge-conflict # checks for files that contain merge conflict strings.
      - id: check-yaml # checks yaml files for parseable syntax.
      - id: detect-private-key # detects the presence of private keys.
      - id: end-of-file-fixer # ensures that a file is either empty, or ends with one newline.
      - id: fix-byte-order-marker # removes utf-8 byte order marker.
      - id: mixed-line-ending # replaces or checks mixed line ending.
      - id: requirements-txt-fixer # sorts entries in requirements.txt.
      - id: trailing-whitespace # trims trailing whitespace.

-   repo: https://github.com/PyCQA/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        name: PEP8

-   repo: https://github.com/PyCQA/isort
    rev: 5.12.0
    hooks:
      - id: isort
        name: Sort imports

- repo: https://github.com/Lucas-C/pre-commit-hooks
  rev: v1.5.3
  hooks:
    - id: insert-license
      files: .*\.py$
      args:
        - --license-filepath
        - assets/file_banner.txt
