name: Close stale issues
on:
  schedule:
    - cron: "0 0 * * *"  # At the end of every day

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          stale-issue-message: |
            👋 Hello, this issue has been automatically marked as stale because it has not had recent activity. Please note it will be closed if no further activity occurs.
            Feel free to inform us of any other **issues** you discover or **feature requests** that come to mind in the future. Pull Requests (PRs) are also always welcomed!
          stale-pr-message: 'This issue has been automatically marked as stale because it has not had recent activity. It will be closed if no further activity occurs.'
          days-before-stale: 10
          days-before-close: 3
          exempt-issue-labels: 'documentation,tutorial'
          operations-per-run: 100  # The maximum number of operations per run, used to control rate limiting.
