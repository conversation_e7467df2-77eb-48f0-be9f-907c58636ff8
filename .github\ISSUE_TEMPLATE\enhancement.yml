name: Enhancement
description: Suggest a BoxMOT enhancement
# title: " "
labels: [enhancement]
body:

  - type: checkboxes
    attributes:
      label: Search before asking
      description: >
        Please search the [issues](https://github.com/mikel-brostrom/boxmot/issues) to see if a similar enhancement request already exists.
      options:
        - label: >
            I have searched the BoxMOT [issues](https://github.com/mikel-brostrom/boxmot/issues) and found no similar enhancement requests.
          required: true

  - type: textarea
    attributes:
      label: Description
      description: A short description of your enhancement.
      placeholder: |
        What new enhancement would you like to see in Yolo Tracking?
    validations:
      required: true

  - type: textarea
    attributes:
      label: Use case
      description: |
        Describe the use case of your feature request. It will help us understand and prioritize the feature request.
      placeholder: |
        How would this feature be used, and who would use it?

  - type: checkboxes
    attributes:
      label: Are you willing to submit a PR?
      description: >
        (Optional) If you have a good understanding of this package and feel like contributing to it we will gladly review your [Pull Request](https://github.com/mikel-brostrom/boxmot/pulls) (PR).
      options:
        - label: Yes I'd like to help by submitting a PR!
