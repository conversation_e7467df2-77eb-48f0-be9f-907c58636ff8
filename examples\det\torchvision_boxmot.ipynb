{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Downloading: \"https://download.pytorch.org/models/fasterrcnn_resnet50_fpn_v2_coco-dd69338a.pth\" to /Users/<USER>/.cache/torch/hub/checkpoints/fasterrcnn_resnet50_fpn_v2_coco-dd69338a.pth\n", "100%|██████████| 167M/167M [00:19<00:00, 9.00MB/s] \n", "\u001b[32m2025-07-28 14:46:08.697\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mboxmot.utils.torch_utils\u001b[0m:\u001b[36mselect_device\u001b[0m:\u001b[36m78\u001b[0m - \u001b[1mYolo Tracking v15.0.2 🚀 Python-3.11.5 torch-2.2.2CPU\u001b[0m\n", "\u001b[32m2025-07-28 14:46:08.698\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mboxmot.appearance.backends.base_backend\u001b[0m:\u001b[36mdownload_model\u001b[0m:\u001b[36m138\u001b[0m - \u001b[1mDownloading ReID weights from %s → %s\u001b[0m\n", "Downloading...\n", "From: https://drive.google.com/uc?id=1sSwXSUlj4_tHZequ_iZ8w_Jh0VaRQMqF\n", "To: /Users/<USER>/boxmot/examples/det/osnet_x0_25_msmt17.pt\n", "100%|██████████| 3.06M/3.06M [00:00<00:00, 7.95MB/s]\n", "\u001b[32m2025-07-28 14:46:14.452\u001b[0m | \u001b[32m\u001b[1mSUCCESS \u001b[0m | \u001b[36mboxmot.appearance.reid.registry\u001b[0m:\u001b[36mload_pretrained_weights\u001b[0m:\u001b[36m64\u001b[0m - \u001b[32m\u001b[1mLoaded pretrained weights from osnet_x0_25_msmt17.pt\u001b[0m\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 28\u001b[39m\n\u001b[32m     26\u001b[39m         tracker.plot_results(bgr, show_trajectories=\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[32m     27\u001b[39m         cv2.imshow(\u001b[33m'\u001b[39m\u001b[33mBoXMOT + Torchvision\u001b[39m\u001b[33m'\u001b[39m, bgr)\n\u001b[32m---> \u001b[39m\u001b[32m28\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m cv2.wait<PERSON><PERSON>(\u001b[32m1\u001b[39m) & \u001b[32m0xFF\u001b[39m == \u001b[38;5;28mord\u001b[39m(\u001b[33m'\u001b[39m\u001b[33mq\u001b[39m\u001b[33m'\u001b[39m): \u001b[38;5;28;01<PERSON>ak\u001b[39;00m\n\u001b[32m     29\u001b[39m cap.release(); cv2.destroyAllWindows()\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["import cv2, torch, numpy as np\n", "from pathlib import Path\n", "from boxmot import BotSort\n", "from torchvision.models.detection import fasterrcnn_resnet50_fpn_v2, FasterRCNN_ResNet50_FPN_V2_Weights as W\n", "\n", "dev = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "w = W.DEFAULT\n", "model = fasterrcnn_resnet50_fpn_v2(weights=w, box_score_thresh=0.5).to(dev).eval()\n", "prep = w.transforms()\n", "tracker = BotSort(reid_weights=Path('osnet_x0_25_msmt17.pt'), device=dev, half=False)\n", "\n", "cap = cv2.VideoCapture(0)\n", "with torch.inference_mode():\n", "    while True:\n", "        ok, bgr = cap.read()\n", "        if not ok: break\n", "        rgb = cv2.cvtColor(bgr, cv2.COLOR_BGR2RGB)\n", "        t = torch.from_numpy(rgb).permute(2,0,1).to(torch.uint8)\n", "        out = model([prep(t).to(dev)])[0]\n", "        s = out['scores'].cpu().numpy()\n", "        keep = s >= 0.5\n", "        dets = np.concatenate([out['boxes'][keep].cpu().numpy(),\n", "                               s[keep,None],\n", "                               out['labels'][keep,None].cpu().numpy()], 1)\n", "        tracker.update(dets, bgr)\n", "        tracker.plot_results(bgr, show_trajectories=True)\n", "        cv2.imshow('BoXMOT + Torchvision', bgr)\n", "        if cv2.wait<PERSON><PERSON>(1) & 0xFF == ord('q'): break\n", "cap.release(); cv2.destroyAllWindows()\n"]}], "metadata": {"kernelspec": {"display_name": "boxmot-YDNZdsaB-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}