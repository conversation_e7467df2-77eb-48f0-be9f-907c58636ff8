{"cells": [{"cell_type": "code", "execution_count": null, "id": "initial_id", "metadata": {"collapsed": true}, "outputs": [], "source": ["# This Python 3 environment comes with many helpful analytics libraries installed\n", "# It is defined by the kaggle/python Docker image: https://github.com/kaggle/docker-python\n", "# For example, here's several helpful packages to load\n", "\n", "import numpy as np # linear algebra\n", "import pandas as pd # data processing, CSV file I/O (e.g. pd.read_csv)\n", "\n", "# Input data files are available in the read-only \"../input/\" directory\n", "# For example, running this (by clicking run or pressing Shift+Enter) will list all files under the input directory\n", "\n", "import os\n", "for dirname, _, filenames in os.walk('/kaggle/input'):\n", "    for filename in filenames:\n", "        print(os.path.join(dirname, filename))\n", "\n", "# You can write up to 20GB to the current directory (/kaggle/working/) that gets preserved as output when you create a version using \"Save & Run All\"\n", "# You can also write temporary files to /kaggle/temp/, but they won't be saved outside of the current session"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["!pip install ultralytics\n", "!git clone https://github.com/KeeganFernandesWork/yolo_tracking\n", "%cd yolo_tracking\n", "!pip install -r requirements.txt\n", "!pip install ."], "id": "15c4686898b0b2e5"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["from boxmot import (OCSORT, BoTSORT, BYTETracker, DeepOCSORT, StrongSORT,\n", "                    create_tracker, get_tracker_config)\n", "from pathlib import Path\n", "import cv2\n", "import sys\n", "import numpy as np\n", "import datetime\n", "from ultralytics import YOLO"], "id": "7518dfb55daa9900"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["sys.path.insert(1, '/kaggle/input/video-weights')\n", "\n", "def create_video_writer(video_cap, output_filename):\n", "\n", "    # grab the width, height, and fps of the frames in the video stream.\n", "    frame_width = int(video_cap.get(cv2.CAP_PROP_FRAME_WIDTH))\n", "    frame_height = int(video_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))\n", "    fps = int(video_cap.get(cv2.CAP_PROP_FPS))\n", "\n", "    # initialize the FourCC and a video writer object\n", "    fourcc = cv2.VideoWriter_fourcc(*'XVID')\n", "    writer = cv2.VideoWriter(output_filename, fourcc, fps,\n", "                             (frame_width, frame_height))\n", "\n", "    return writer"], "id": "53aacf3ee03f43b"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["color = (0, 0, 255)  # BGR\n", "thickness = 2\n", "fontscale = 0.5\n", "\n", "device = \"cuda:0\" # cuda:0 , cpu\n", "fp16 = True # True if gpu available\n", "# load the pre-trained YOLOv8n model\n", "model = YOLO(\"/kaggle/input/video-weights/best.pt\")\n", "source = \"/kaggle/input/video-weights/test_video2.mp4\""], "id": "acb25c61ba4d8c7e"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "help(DeepOCSORT)", "id": "8542af91021f4a75"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["tracker = DeepOCSORT(\n", "    model_weights=Path('osnet_x0_25_msmt17.pt'), # which ReID model to use\n", "    device=device,\n", "    fp16=fp16,\n", ")\n", "vid = cv2.VideoCapture(source)\n", "writer = create_video_writer(vid, \"DeepOCSORT.mp4\")\n", "\n", "while True:\n", "    ret, im = vid.read()\n", "\n", "    detections = model.predict(im )[0]\n", "\n", "    # initialize the list of bounding boxes and confidences\n", "    results = []\n", "    if not ret:\n", "        break\n", "\n", "    if np.array(detections.boxes.data.tolist()).ndim < 2:\n", "        results = [[0, 0, 0, 0, 0.0922948837280273, 0]]\n", "\n", "    ts = tracker.update(np.array(detections.boxes.data.tolist()), im) # --> (x, y, x, y, id, conf, cls)\n", "\n", "    xyxys = ts[:,0:4].astype('int') # float64 to int\n", "    ids = ts[:, 4].astype('int') # float64 to int\n", "    confs = ts[:, 5]\n", "    clss = ts[:, 6]\n", "\n", "    # print bboxes with their associated id, cls and conf\n", "    if ts.shape[0] != 0:\n", "        for xyxy, id, conf, cls in zip(xyxys, ids, confs, clss):\n", "            im = cv2.rectangle(\n", "                im,\n", "                (xyxy[0], xyxy[1]),\n", "                (xyxy[2], xyxy[3]),\n", "                color,\n", "                thickness\n", "            )\n", "            cv2.putText(\n", "                im,\n", "                f'id: {id}, conf: {conf}, c: {cls}',\n", "                (xyxy[0], xyxy[1]-10),\n", "                cv2.FONT_HERSHEY_SIMPLEX,\n", "                fontscale,\n", "                color,\n", "                thickness\n", "            )\n", "\n", "    # show the frame to our screen\n", "\n", "    writer.write(im)\n", "\n", "\n", "vid.release()\n", "writer.release()\n", "print(\"Task Completed\")"], "id": "9f77d3d06d1c4f15"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "help(StrongSORT)", "id": "b2221ae4515b13fb"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["tracker = StrongSORT(\n", "    model_weights=Path('mobilenetv2_x1_4_dukemtmcreid.pt'), # which ReID model to use\n", "    device=device,\n", "    fp16=fp16,\n", ")\n", "tracker.n_init = 1\n", "vid = cv2.VideoCapture(source)\n", "writer = create_video_writer(vid, \"StrongSort.mp4\")\n", "while True:\n", "    ret, im = vid.read()\n", "\n", "    detections = model.predict(im )[0]\n", "\n", "    # initialize the list of bounding boxes and confidences\n", "    results = []\n", "    if not ret:\n", "        break\n", "\n", "    if np.array(detections.boxes.data.tolist()).ndim < 2:\n", "        results = [[0, 0, 0, 0, 0.0922948837280273, 0]]\n", "    ts = tracker.update(np.array(detections.boxes.data.tolist()), im) # --> (x, y, x, y, id, conf, cls)\n", "    ts = tracker.update(np.array(detections.boxes.data.tolist()), im) # --> (x, y, x, y, id, conf, cls)\n", "    ts = tracker.update(np.array(detections.boxes.data.tolist()), im) # --> (x, y, x, y, id, conf, cls)\n", "\n", "    xyxys = ts[:,0:4].astype('int') # float64 to int\n", "    ids = ts[:, 4].astype('int') # float64 to int\n", "    confs = ts[:, 5]\n", "    clss = ts[:, 6]\n", "\n", "    # print bboxes with their associated id, cls and conf\n", "    if ts.shape[0] != 0:\n", "        for xyxy, id, conf, cls in zip(xyxys, ids, confs, clss):\n", "            im = cv2.rectangle(\n", "                im,\n", "                (xyxy[0], xyxy[1]),\n", "                (xyxy[2], xyxy[3]),\n", "                color,\n", "                thickness\n", "            )\n", "            cv2.putText(\n", "                im,\n", "                f'id: {id}, conf: {conf}, c: {cls}',\n", "                (xyxy[0], xyxy[1]-10),\n", "                cv2.FONT_HERSHEY_SIMPLEX,\n", "                fontscale,\n", "                color,\n", "                thickness\n", "            )\n", "\n", "    # show the frame to our screen\n", "\n", "    writer.write(im)\n", "\n", "\n", "vid.release()\n", "writer.release()"], "id": "45ca36272e62815a"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "help(BoTSORT)", "id": "c6d12d7089f3d578"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["from boxmot import BoTSORT\n", "tracker = BoTSORT(\n", "    model_weights=Path('osnet_x0_25_msmt17.pt'),\n", "    device=device,\n", "    fp16=fp16,\n", ")\n", "vid = cv2.VideoCapture(source)\n", "writer = create_video_writer(vid, \"BoTSORT.mp4\")\n", "\n", "while True:\n", "    ret, im = vid.read()\n", "\n", "    detections = model.predict(im )[0]\n", "\n", "    # initialize the list of bounding boxes and confidences\n", "    results = []\n", "    if not ret:\n", "        break\n", "\n", "    if np.array(detections.boxes.data.tolist()).ndim < 2:\n", "        results = [[0, 0, 0, 0, 0.0922948837280273, 0]] # --> (x, y, x, y, id, conf, cls)\n", "    ts = tracker.update(np.array(detections.boxes.data.tolist()), im) # --> (x, y, x, y, id, conf, cls)\n", "\n", "    xyxys = ts[:,0:4].astype('int') # float64 to int\n", "    ids = ts[:, 4].astype('int') # float64 to int\n", "    confs = ts[:, 5]\n", "    clss = ts[:, 6]\n", "\n", "    # print bboxes with their associated id, cls and conf\n", "    if ts.shape[0] != 0:\n", "        for xyxy, id, conf, cls in zip(xyxys, ids, confs, clss):\n", "            im = cv2.rectangle(\n", "                im,\n", "                (xyxy[0], xyxy[1]),\n", "                (xyxy[2], xyxy[3]),\n", "                color,\n", "                thickness\n", "            )\n", "            cv2.putText(\n", "                im,\n", "                f'id: {id}, conf: {conf}, c: {cls}',\n", "                (xyxy[0], xyxy[1]-10),\n", "                cv2.FONT_HERSHEY_SIMPLEX,\n", "                fontscale,\n", "                color,\n", "                thickness\n", "            )\n", "\n", "    # show the frame to our screen\n", "\n", "    writer.write(im)\n", "\n", "\n", "vid.release()\n", "writer.release()"], "id": "c4d9d02e6de4521e"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "help(BYTETracker)", "id": "a92050f4f4e30bbf"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["from boxmot import BYTETracker\n", "tracker =  BYTETracker()\n", "vid = cv2.VideoCapture(source)\n", "writer = create_video_writer(vid, \"BYTETracker.mp4\")\n", "\n", "while True:\n", "    ret, im = vid.read()\n", "\n", "    detections = model.predict(im )[0]\n", "\n", "    # initialize the list of bounding boxes and confidences\n", "    results = []\n", "    if not ret:\n", "        break\n", "\n", "    if np.array(detections.boxes.data.tolist()).ndim < 2:\n", "        results = [[0, 0, 0, 0, 0.0922948837280273, 0]]# --> (x, y, x, y, id, conf, cls)\n", "    ts = tracker.update(np.array(detections.boxes.data.tolist()), im) # --> (x, y, x, y, id, conf, cls)\n", "\n", "    xyxys = ts[:,0:4].astype('int') # float64 to int\n", "    ids = ts[:, 4].astype('int') # float64 to int\n", "    confs = ts[:, 5]\n", "    clss = ts[:, 6]\n", "\n", "    # print bboxes with their associated id, cls and conf\n", "    if ts.shape[0] != 0:\n", "        for xyxy, id, conf, cls in zip(xyxys, ids, confs, clss):\n", "            im = cv2.rectangle(\n", "                im,\n", "                (xyxy[0], xyxy[1]),\n", "                (xyxy[2], xyxy[3]),\n", "                color,\n", "                thickness\n", "            )\n", "            cv2.putText(\n", "                im,\n", "                f'id: {id}, conf: {conf}, c: {cls}',\n", "                (xyxy[0], xyxy[1]-10),\n", "                cv2.FONT_HERSHEY_SIMPLEX,\n", "                fontscale,\n", "                color,\n", "                thickness\n", "            )\n", "\n", "    # show the frame to our screen\n", "\n", "    writer.write(im)\n", "\n", "\n", "vid.release()\n", "writer.release()"], "id": "4141e7eb39ed0c25"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["from base64 import b64encode\n", "from IPython.display import HTML\n", "def play(filename):\n", "    html = ''\n", "    video = open(filename,'rb').read()\n", "    src = 'data:video/mp4;base64,' + b64encode(video).decode()\n", "    html += '<video width=400 height=300 controls autoplay loop><source src=\"%s\" type=\"video/mp4\"></video>' % src\n", "    return HTML(html)"], "id": "cb10680e92d984d6"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "play(source)", "id": "5635a3cd65c06399"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "play(\"/kaggle/working/yolo_tracking/BYTETracker.mp4\")", "id": "55d2a9f73ef1302f"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "play(\"/kaggle/working/yolo_tracking/BoTSORT.mp4\")", "id": "c3f4eb5c3d50e645"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "play(\"/kaggle/working/yolo_tracking/StrongSort.mp4\")", "id": "4dc9b087d3dfdaf9"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "play(\"/kaggle/working/yolo_tracking/DeepOCSORT.mp4\")", "id": "51a6ec5f09e7b8a6"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}